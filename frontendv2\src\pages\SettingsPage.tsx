import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { type User } from '../utils/api'
import { settingsAPI, type AllSettings } from '../utils/settingsAPI'

interface SettingsPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function SettingsPage({ currentUser, onLogout }: SettingsPageProps) {
  const navigate = useNavigate();
  const [settings, setSettings] = useState<AllSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'hours' | 'payment'>('general');

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }

    fetchSettings();
  }, [currentUser, navigate]);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await settingsAPI.getAllSettings();
      if (response.success) {
        setSettings(response.data);
      } else {
        console.error('Failed to fetch settings');
        setError('Failed to load settings. Please try again.');
        // Set default settings if fetch fails
        setSettings({
          site: {
            general: {
              siteName: '',
              siteDescription: '',
              contactPhone: '',
              contactEmail: '',
              address: '',
              timezone: 'America/New_York',
              currency: 'USD',
              language: 'en'
            },
            features: {
              enableAppointments: true,
              enableEcommerce: false,
              enableReviews: false,
              enableLoyaltyProgram: false,
              enableGiftCards: false,
              enableWaitlist: false,
              enableReferrals: false
            },
            notifications: {
              emailNotifications: true,
              smsNotifications: false,
              pushNotifications: false,
              appointmentReminders: true,
              marketingEmails: false
            }
          },
          payment: {
            methods: {
              cashApp: { enabled: false, handle: '' },
              zelle: { enabled: false, email: '', phone: '' },
              venmo: { enabled: false, handle: '' },
              paypal: { enabled: false, email: '' }
            },
            policies: {
              requireDeposit: false,
              depositAmount: 0,
              depositPercentage: 0,
              cancellationPolicy: '',
              refundPolicy: ''
            }
          },
          theme: {
            colors: {
              primary: '',
              secondary: '',
              accent: '',
              background: '',
              text: ''
            },
            fonts: {
              heading: '',
              body: ''
            },
            layout: {
              headerStyle: '',
              footerStyle: '',
              sidebarPosition: ''
            }
          },
          seo: {
            meta: {
              title: '',
              description: '',
              keywords: '',
              author: ''
            },
            social: {
              ogTitle: '',
              ogDescription: '',
              ogImage: '',
              twitterCard: ''
            },
            analytics: {
              googleAnalyticsId: '',
              facebookPixelId: '',
              googleTagManagerId: ''
            }
          }
        });
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      setError('Failed to load settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    try {
      // Save all settings sections
      await Promise.all([
        settingsAPI.updateSiteSettings(settings.site),
        settingsAPI.updatePaymentSettings(settings.payment),
        settingsAPI.updateThemeSettings(settings.theme),
        settingsAPI.updateSEOSettings(settings.seo)
      ]);

      alert('Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings. Please try again.');
    }
  };

  const handleSiteSettingChange = (field: string, value: any) => {
    if (!settings) return;
    setSettings(prev => ({
      ...prev!,
      site: {
        ...prev!.site,
        general: {
          ...prev!.site.general,
          [field]: value
        }
      }
    }));
  };

  const handlePaymentSettingChange = (method: string, field: string, value: any) => {
    if (!settings) return;
    setSettings(prev => {
      if (!prev) return prev;
      const currentMethod = prev.payment.methods[method as keyof typeof prev.payment.methods];
      return {
        ...prev,
        payment: {
          ...prev.payment,
          methods: {
            ...prev.payment.methods,
            [method]: {
              ...currentMethod,
              [field]: value
            }
          }
        }
      };
    });
  };

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px', textAlign: 'center' }}>
            <p style={{ color: '#6b7280', fontSize: '16px' }}>Loading settings...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
          <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: '200px', textAlign: 'center' }}>
            <p style={{ color: '#ef4444', fontSize: '16px', marginBottom: '1rem' }}>{error}</p>
            <button
              style={{
                background: '#2c5530',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
              onClick={() => {
                setError(null);
                fetchSettings();
              }}
              onMouseOver={(e) => (e.target as HTMLButtonElement).style.background = '#1e3a22'}
              onMouseOut={(e) => (e.target as HTMLButtonElement).style.background = '#2c5530'}
            >
              Try Again
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <Header currentUser={currentUser} onLogout={onLogout} />

      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '2rem',
          paddingBottom: '1rem',
          borderBottom: '2px solid #d4af37'
        }}>
          <h1 style={{ color: '#2c5530', fontSize: '2.5rem', margin: '0' }}>Settings</h1>
          <button
            style={{
              background: '#2c5530',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}
            onClick={handleSaveSettings}
            onMouseOver={(e) => (e.target as HTMLButtonElement).style.background = '#1e3a22'}
            onMouseOut={(e) => (e.target as HTMLButtonElement).style.background = '#2c5530'}
          >
            Save Settings
          </button>
        </div>

        <div style={{
          display: 'flex',
          gap: '1rem',
          marginBottom: '2rem',
          borderBottom: '2px solid #e0e0e0'
        }}>
          <button
            style={{
              background: 'none',
              border: 'none',
              padding: '1rem 1.5rem',
              fontSize: '1rem',
              fontWeight: '600',
              color: activeTab === 'general' ? '#2c5530' : '#666',
              cursor: 'pointer',
              borderBottom: activeTab === 'general' ? '3px solid #d4af37' : '3px solid transparent',
              transition: 'all 0.3s ease'
            }}
            onClick={() => setActiveTab('general')}
            onMouseOver={(e) => {
              if (activeTab !== 'general') {
                (e.target as HTMLButtonElement).style.color = '#2c5530';
              }
            }}
            onMouseOut={(e) => {
              if (activeTab !== 'general') {
                (e.target as HTMLButtonElement).style.color = '#666';
              }
            }}
          >
            General
          </button>
          <button
            style={{
              background: 'none',
              border: 'none',
              padding: '1rem 1.5rem',
              fontSize: '1rem',
              fontWeight: '600',
              color: activeTab === 'hours' ? '#2c5530' : '#666',
              cursor: 'pointer',
              borderBottom: activeTab === 'hours' ? '3px solid #d4af37' : '3px solid transparent',
              transition: 'all 0.3s ease'
            }}
            onClick={() => setActiveTab('hours')}
            onMouseOver={(e) => {
              if (activeTab !== 'hours') {
                (e.target as HTMLButtonElement).style.color = '#2c5530';
              }
            }}
            onMouseOut={(e) => {
              if (activeTab !== 'hours') {
                (e.target as HTMLButtonElement).style.color = '#666';
              }
            }}
          >
            Working Hours
          </button>
          <button
            style={{
              background: 'none',
              border: 'none',
              padding: '1rem 1.5rem',
              fontSize: '1rem',
              fontWeight: '600',
              color: activeTab === 'payment' ? '#2c5530' : '#666',
              cursor: 'pointer',
              borderBottom: activeTab === 'payment' ? '3px solid #d4af37' : '3px solid transparent',
              transition: 'all 0.3s ease'
            }}
            onClick={() => setActiveTab('payment')}
            onMouseOver={(e) => {
              if (activeTab !== 'payment') {
                (e.target as HTMLButtonElement).style.color = '#2c5530';
              }
            }}
            onMouseOut={(e) => {
              if (activeTab !== 'payment') {
                (e.target as HTMLButtonElement).style.color = '#666';
              }
            }}
          >
            Payment Methods
          </button>
        </div>

        {activeTab === 'general' && settings && (
          <div style={{
            background: 'white',
            borderRadius: '12px',
            padding: '32px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            marginBottom: '30px'
          }}>
            <h2 style={{ margin: '0 0 24px 0', color: '#1f2937', fontSize: '24px', fontWeight: '700' }}>
              General Settings
            </h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Site Name</label>
                <input
                  type="text"
                  value={settings.site.general.siteName}
                  onChange={(e) => handleSiteSettingChange('siteName', e.target.value)}
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '16px',
                    transition: 'border-color 0.2s'
                  }}
                  onFocus={(e) => {
                    (e.target as HTMLInputElement).style.borderColor = '#3b82f6';
                    (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    (e.target as HTMLInputElement).style.borderColor = '#d1d5db';
                    (e.target as HTMLInputElement).style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Site Description</label>
                <textarea
                  value={settings.site.general.siteDescription}
                  onChange={(e) => handleSiteSettingChange('siteDescription', e.target.value)}
                  rows={3}
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '16px',
                    transition: 'border-color 0.2s',
                    fontFamily: 'inherit',
                    resize: 'vertical'
                  }}
                  onFocus={(e) => {
                    (e.target as HTMLTextAreaElement).style.borderColor = '#3b82f6';
                    (e.target as HTMLTextAreaElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    (e.target as HTMLTextAreaElement).style.borderColor = '#d1d5db';
                    (e.target as HTMLTextAreaElement).style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Contact Phone</label>
                <input
                  type="text"
                  value={settings.site.general.contactPhone}
                  onChange={(e) => handleSiteSettingChange('contactPhone', e.target.value)}
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '16px',
                    transition: 'border-color 0.2s'
                  }}
                  onFocus={(e) => {
                    (e.target as HTMLInputElement).style.borderColor = '#3b82f6';
                    (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    (e.target as HTMLInputElement).style.borderColor = '#d1d5db';
                    (e.target as HTMLInputElement).style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Contact Email</label>
                <input
                  type="email"
                  value={settings.site.general.contactEmail}
                  onChange={(e) => handleSiteSettingChange('contactEmail', e.target.value)}
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '16px',
                    transition: 'border-color 0.2s'
                  }}
                  onFocus={(e) => {
                    (e.target as HTMLInputElement).style.borderColor = '#3b82f6';
                    (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    (e.target as HTMLInputElement).style.borderColor = '#d1d5db';
                    (e.target as HTMLInputElement).style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Address</label>
                <input
                  type="text"
                  value={settings.site.general.address}
                  onChange={(e) => handleSiteSettingChange('address', e.target.value)}
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '16px',
                    transition: 'border-color 0.2s'
                  }}
                  onFocus={(e) => {
                    (e.target as HTMLInputElement).style.borderColor = '#3b82f6';
                    (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    (e.target as HTMLInputElement).style.borderColor = '#d1d5db';
                    (e.target as HTMLInputElement).style.boxShadow = 'none';
                  }}
                />
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Timezone</label>
                <select
                  value={settings.site.general.timezone}
                  onChange={(e) => handleSiteSettingChange('timezone', e.target.value)}
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '16px',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white'
                  }}
                  onFocus={(e) => {
                    (e.target as HTMLSelectElement).style.borderColor = '#3b82f6';
                    (e.target as HTMLSelectElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    (e.target as HTMLSelectElement).style.borderColor = '#d1d5db';
                    (e.target as HTMLSelectElement).style.boxShadow = 'none';
                  }}
                >
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                </select>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Currency</label>
                <select
                  value={settings.site.general.currency}
                  onChange={(e) => handleSiteSettingChange('currency', e.target.value)}
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '16px',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white'
                  }}
                  onFocus={(e) => {
                    (e.target as HTMLSelectElement).style.borderColor = '#3b82f6';
                    (e.target as HTMLSelectElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    (e.target as HTMLSelectElement).style.borderColor = '#d1d5db';
                    (e.target as HTMLSelectElement).style.boxShadow = 'none';
                  }}
                >
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="GBP">GBP (£)</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'hours' && settings && (
          <div style={{
            background: 'white',
            borderRadius: '12px',
            padding: '32px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            marginBottom: '30px'
          }}>
            <h2 style={{ margin: '0 0 24px 0', color: '#1f2937', fontSize: '24px', fontWeight: '700' }}>
              Working Hours
            </h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <p style={{ color: '#6b7280', fontSize: '16px', margin: '0' }}>
                Working hours management will be available in a future update.
              </p>
              <p style={{ color: '#6b7280', fontSize: '16px', margin: '0' }}>
                For now, you can manage business hours through the business profile settings.
              </p>
            </div>
          </div>
        )}

        {activeTab === 'payment' && settings && (
          <div style={{
            background: 'white',
            borderRadius: '12px',
            padding: '32px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            marginBottom: '30px'
          }}>
            <h2 style={{ margin: '0 0 24px 0', color: '#1f2937', fontSize: '24px', fontWeight: '700' }}>
              Payment Methods
            </h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
              <div style={{
                padding: '24px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                backgroundColor: '#f9fafb'
              }}>
                <h3 style={{ margin: '0 0 16px 0', color: '#1f2937', fontSize: '18px', fontWeight: '600' }}>
                  Cash App
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <label style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    color: '#374151',
                    fontWeight: '600',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}>
                    <input
                      type="checkbox"
                      checked={settings.payment.methods.cashApp.enabled}
                      onChange={(e) => handlePaymentSettingChange('cashApp', 'enabled', e.target.checked)}
                      style={{ marginRight: '8px' }}
                    />
                    Enable Cash App
                  </label>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Cash App Handle</label>
                    <input
                      type="text"
                      value={settings.payment.methods.cashApp.handle}
                      onChange={(e) => handlePaymentSettingChange('cashApp', 'handle', e.target.value)}
                      placeholder="$username"
                      disabled={!settings.payment.methods.cashApp.enabled}
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '16px',
                        transition: 'border-color 0.2s',
                        backgroundColor: settings.payment.methods.cashApp.enabled ? 'white' : '#f9fafb',
                        color: settings.payment.methods.cashApp.enabled ? '#000' : '#9ca3af',
                        cursor: settings.payment.methods.cashApp.enabled ? 'text' : 'not-allowed'
                      }}
                      onFocus={(e) => {
                        if (settings.payment.methods.cashApp.enabled) {
                          (e.target as HTMLInputElement).style.borderColor = '#3b82f6';
                          (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                        }
                      }}
                      onBlur={(e) => {
                        (e.target as HTMLInputElement).style.borderColor = '#d1d5db';
                        (e.target as HTMLInputElement).style.boxShadow = 'none';
                      }}
                    />
                  </div>
                </div>
              </div>

              <div style={{
                padding: '24px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                backgroundColor: '#f9fafb'
              }}>
                <h3 style={{ margin: '0 0 16px 0', color: '#1f2937', fontSize: '18px', fontWeight: '600' }}>
                  Zelle
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <label style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    color: '#374151',
                    fontWeight: '600',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}>
                    <input
                      type="checkbox"
                      checked={settings.payment.methods.zelle.enabled}
                      onChange={(e) => handlePaymentSettingChange('zelle', 'enabled', e.target.checked)}
                      style={{ marginRight: '8px' }}
                    />
                    Enable Zelle
                  </label>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Zelle Email</label>
                    <input
                      type="email"
                      value={settings.payment.methods.zelle.email}
                      onChange={(e) => handlePaymentSettingChange('zelle', 'email', e.target.value)}
                      placeholder="<EMAIL>"
                      disabled={!settings.payment.methods.zelle.enabled}
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '16px',
                        transition: 'border-color 0.2s',
                        backgroundColor: settings.payment.methods.zelle.enabled ? 'white' : '#f9fafb',
                        color: settings.payment.methods.zelle.enabled ? '#000' : '#9ca3af',
                        cursor: settings.payment.methods.zelle.enabled ? 'text' : 'not-allowed'
                      }}
                      onFocus={(e) => {
                        if (settings.payment.methods.zelle.enabled) {
                          (e.target as HTMLInputElement).style.borderColor = '#3b82f6';
                          (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                        }
                      }}
                      onBlur={(e) => {
                        (e.target as HTMLInputElement).style.borderColor = '#d1d5db';
                        (e.target as HTMLInputElement).style.boxShadow = 'none';
                      }}
                    />
                  </div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    <label style={{ color: '#374151', fontWeight: '600', fontSize: '14px' }}>Zelle Phone</label>
                    <input
                      type="text"
                      value={settings.payment.methods.zelle.phone}
                      onChange={(e) => handlePaymentSettingChange('zelle', 'phone', e.target.value)}
                      placeholder="(*************"
                      disabled={!settings.payment.methods.zelle.enabled}
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '16px',
                        transition: 'border-color 0.2s',
                        backgroundColor: settings.payment.methods.zelle.enabled ? 'white' : '#f9fafb',
                        color: settings.payment.methods.zelle.enabled ? '#000' : '#9ca3af',
                        cursor: settings.payment.methods.zelle.enabled ? 'text' : 'not-allowed'
                      }}
                      onFocus={(e) => {
                        if (settings.payment.methods.zelle.enabled) {
                          (e.target as HTMLInputElement).style.borderColor = '#3b82f6';
                          (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                        }
                      }}
                      onBlur={(e) => {
                        (e.target as HTMLInputElement).style.borderColor = '#d1d5db';
                        (e.target as HTMLInputElement).style.boxShadow = 'none';
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
