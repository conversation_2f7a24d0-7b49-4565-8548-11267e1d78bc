import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { serviceAPI, type Service } from '../utils/serviceAPI'
import { type User } from '../utils/api'

interface ServicesPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function ServicesPage({ currentUser, onLogout }: ServicesPageProps) {
  const navigate = useNavigate();
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'main' | 'addons' | 'active' | 'inactive'>('all');
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }

    fetchServices();
  }, [currentUser, navigate]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await serviceAPI.getServices({
        page: 1,
        limit: 100,
        sortBy: 'category',
        sortOrder: 'asc'
      });

      if (response.success) {
        setServices(response.data.services);
      } else {
        console.error('Failed to fetch services');
        setError('Failed to load services. Please try again.');
        setServices([]);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      setError('Failed to load services. Please try again.');
      setServices([]);
    } finally {
      setLoading(false);
    }
  };

  const handleServiceEdit = async (serviceId: string) => {
    try {
      const response = await serviceAPI.getService(serviceId);
      if (response.success) {
        const service = response.data;
        const newPrice = prompt('Enter new price:', service.price.toString());
        if (newPrice !== null && !isNaN(parseFloat(newPrice))) {
          const updateResponse = await serviceAPI.updateService(serviceId, {
            price: parseFloat(newPrice)
          });
          if (updateResponse.success) {
            alert('Service updated successfully!');
            fetchServices();
          } else {
            alert('Failed to update service');
          }
        }
      }
    } catch (error) {
      console.error('Error updating service:', error);
      alert('Failed to update service');
    }
  };

  const handleServiceDelete = async (serviceId: string) => {
    if (confirm('Are you sure you want to delete this service?')) {
      try {
        const response = await serviceAPI.deleteService(serviceId);
        if (response.success) {
          alert('Service deleted successfully!');
          fetchServices();
        } else {
          alert('Failed to delete service');
        }
      } catch (error) {
        console.error('Error deleting service:', error);
        alert('Failed to delete service');
      }
    }
  };

  const handleAddService = () => {
    const name = prompt('Enter service name:');
    if (!name) return;

    const description = prompt('Enter service description:');
    if (!description) return;

    const category = prompt('Enter service category:');
    if (!category) return;

    const duration = prompt('Enter duration (in minutes):');
    if (!duration || isNaN(parseInt(duration))) return;

    const price = prompt('Enter price:');
    if (!price || isNaN(parseFloat(price))) return;

    createService({
      name,
      description,
      category,
      duration: parseInt(duration),
      price: parseFloat(price),
      isActive: true
    });
  };

  const createService = async (serviceData: any) => {
    try {
      const response = await serviceAPI.createService(serviceData);
      if (response.success) {
        alert('Service created successfully!');
        fetchServices();
      } else {
        alert('Failed to create service');
      }
    } catch (error) {
      console.error('Error creating service:', error);
      alert('Failed to create service');
    }
  };

  const filteredServices = services.filter(service => {
    switch (filter) {
      case 'main':
        return service.category !== 'Add-ons';
      case 'addons':
        return service.category === 'Add-ons';
      case 'active':
        return service.isActive;
      case 'inactive':
        return !service.isActive;
      default:
        return true;
    }
  });

  const getStatusColor = (isActive: boolean) => {
    return isActive ? '#10b981' : '#ef4444';
  };

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px', textAlign: 'center' }}>
            <p style={{ color: '#6b7280', fontSize: '16px' }}>Loading services...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
          <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: '200px', textAlign: 'center' }}>
            <p style={{ color: '#ef4444', fontSize: '16px', marginBottom: '1rem' }}>{error}</p>
            <button
              style={{
                background: '#2c5530',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
              onClick={() => {
                setError(null);
                fetchServices();
              }}
              onMouseOver={(e) => (e.target as HTMLButtonElement).style.background = '#1e3a22'}
              onMouseOut={(e) => (e.target as HTMLButtonElement).style.background = '#2c5530'}
            >
              Try Again
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <Header currentUser={currentUser} onLogout={onLogout} />

      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '2rem',
          paddingBottom: '1rem',
          borderBottom: '2px solid #d4af37'
        }}>
          <h1 style={{ color: '#2c5530', fontSize: '2.5rem', margin: '0' }}>Services Management</h1>
          <button
            style={{
              background: '#2c5530',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}
            onClick={handleAddService}
            onMouseOver={(e) => (e.target as HTMLButtonElement).style.background = '#1e3a22'}
            onMouseOut={(e) => (e.target as HTMLButtonElement).style.background = '#2c5530'}
          >
            Add New Service
          </button>
        </div>

        <div style={{
          display: 'flex',
          gap: '1rem',
          marginBottom: '2rem',
          borderBottom: '2px solid #e0e0e0'
        }}>
          {(['all', 'main', 'addons', 'active', 'inactive'] as const).map(filterType => (
            <button
              key={filterType}
              style={{
                background: 'none',
                border: 'none',
                padding: '1rem 1.5rem',
                fontSize: '1rem',
                fontWeight: '600',
                color: filter === filterType ? '#2c5530' : '#666',
                cursor: 'pointer',
                borderBottom: filter === filterType ? '3px solid #d4af37' : '3px solid transparent',
                transition: 'all 0.3s ease'
              }}
              onClick={() => setFilter(filterType)}
              onMouseOver={(e) => {
                if (filter !== filterType) {
                  (e.target as HTMLButtonElement).style.color = '#2c5530';
                }
              }}
              onMouseOut={(e) => {
                if (filter !== filterType) {
                  (e.target as HTMLButtonElement).style.color = '#666';
                }
              }}
            >
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
            </button>
          ))}
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {filteredServices.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '3rem',
              color: '#6b7280',
              fontSize: '16px'
            }}>
              <p>No services found for the selected filter.</p>
            </div>
          ) : (
            filteredServices.map(service => (
              <div
                key={service.id}
                style={{
                  background: '#f8f9fa',
                  borderRadius: '8px',
                  padding: '1.5rem',
                  border: '1px solid #e9ecef',
                  transition: 'box-shadow 0.3s ease'
                }}
                onMouseOver={(e) => (e.currentTarget as HTMLDivElement).style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)'}
                onMouseOut={(e) => (e.currentTarget as HTMLDivElement).style.boxShadow = 'none'}
              >
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                  marginBottom: '1rem'
                }}>
                  <h3 style={{ fontSize: '1.1rem', fontWeight: '600', color: '#333', margin: '0' }}>
                    {service.name}
                  </h3>
                  <span
                    style={{
                      backgroundColor: getStatusColor(service.isActive),
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      textTransform: 'uppercase'
                    }}
                  >
                    {service.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>

                <div style={{ marginBottom: '1rem' }}>
                  <p style={{ margin: '0.5rem 0', color: '#666', fontSize: '0.9rem' }}>
                    <strong>Description:</strong> {service.description}
                  </p>
                  <p style={{ margin: '0.5rem 0', color: '#666', fontSize: '0.9rem' }}>
                    <strong>Category:</strong> {service.category}
                  </p>
                  <p style={{ margin: '0.5rem 0', color: '#666', fontSize: '0.9rem' }}>
                    <strong>Duration:</strong> {service.duration} minutes
                  </p>
                  <p style={{ margin: '0.5rem 0', color: '#666', fontSize: '0.9rem' }}>
                    <strong>Price:</strong> ${service.price}
                  </p>
                </div>

                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <button
                    style={{
                      background: '#d4af37',
                      color: 'white',
                      border: 'none',
                      padding: '0.5rem 1rem',
                      borderRadius: '4px',
                      fontWeight: '600',
                      fontSize: '0.9rem',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onClick={() => handleServiceEdit(service.id)}
                    onMouseOver={(e) => (e.target as HTMLButtonElement).style.background = '#b8941f'}
                    onMouseOut={(e) => (e.target as HTMLButtonElement).style.background = '#d4af37'}
                  >
                    Edit
                  </button>
                  <button
                    style={{
                      background: '#dc3545',
                      color: 'white',
                      border: 'none',
                      padding: '0.5rem 1rem',
                      borderRadius: '4px',
                      fontWeight: '600',
                      fontSize: '0.9rem',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onClick={() => handleServiceDelete(service.id)}
                    onMouseOver={(e) => (e.target as HTMLButtonElement).style.background = '#c82333'}
                    onMouseOut={(e) => (e.target as HTMLButtonElement).style.background = '#dc3545'}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </main>
    </div>
  )
}
